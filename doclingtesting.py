import os
import warnings
from pathlib import Path
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.document_converter import PdfFormatOption

# Try to import torch to check for GPU availability
try:
    import torch
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE_NAME = torch.cuda.get_device_name(0) if GPU_AVAILABLE else "CPU"
    DEVICE_COUNT = torch.cuda.device_count() if GPU_AVAILABLE else 0
except ImportError:
    GPU_AVAILABLE = False
    DEVICE_NAME = "CPU"
    DEVICE_COUNT = 0

# Only suppress pin_memory warnings when using CPU (no GPU available)
if not GPU_AVAILABLE:
    warnings.filterwarnings("ignore", message=".*pin_memory.*")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")

# --- Configuration ---

# 1. Determine the path to your Desktop
# This works for Windows, macOS, and Linux
desktop_path = Path.home() / "Desktop"
# Filter to only look for PDF files
pdf_files = list(desktop_path.glob("*.pdf"))

if not pdf_files:
    print(f"ERROR: No PDF files found in {desktop_path}")
    exit()

# 2. Initialize the Document Converter with optimal settings
# Configure pipeline options for processing
pipeline_options = PdfPipelineOptions()
pipeline_options.do_ocr = True  # Enable OCR for image text extraction
pipeline_options.do_table_structure = True  # Enable table structure recognition

# Create format options for PDF processing
pdf_format_option = PdfFormatOption(
    pipeline_options=pipeline_options
)

# Initialize converter with optimal configuration (will use GPU if available)
converter = DocumentConverter(
    format_options={
        InputFormat.PDF: pdf_format_option
    }
)

# Display device information
print("🖥️  DEVICE INFORMATION")
print("="*50)
if GPU_AVAILABLE:
    print(f"✅ GPU acceleration: ENABLED")
    print(f"🎮 GPU device: {DEVICE_NAME}")
    print(f"🔢 GPU count: {DEVICE_COUNT}")
    print(f"💾 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print(f"⚠️  GPU acceleration: NOT AVAILABLE")
    print(f"🖥️  Fallback device: CPU")
    print(f"ℹ️  Note: Install CUDA-compatible PyTorch for GPU acceleration")
print("="*50)

# --- Processing ---

print("-" * 50)
print(f"Found {len(pdf_files)} PDF(s) on the Desktop. Starting conversion...")
print("-" * 50)

# Iterate over each PDF file found
for i, pdf_path in enumerate(pdf_files, 1):
    print(f"\n📄 Processing ({i}/{len(pdf_files)}): {pdf_path.name}")
    print(f"📁 File size: {pdf_path.stat().st_size / 1024:.1f} KB")
    print("-" * 20)

    try:
        print("🔄 Converting document...")
        # Convert the document using its local file path
        result = converter.convert(str(pdf_path))

        # Access the structured document object
        doc = result.document

        print("📝 Extracting content...")
        # Export the document content as Markdown for clean, readable text
        markdown_content = doc.export_to_markdown()

        # Print document statistics
        print(f"✅ Extraction complete!")
        print(f"📊 Content length: {len(markdown_content)} characters")
        print(f"📄 Number of pages: {len(doc.pages) if hasattr(doc, 'pages') else 'Unknown'}")
        print("\n" + "="*60)
        print("EXTRACTED CONTENT:")
        print("="*60)

        # Print the extracted content
        print(markdown_content)

    except Exception as e:
        print(f"❌ An error occurred processing {pdf_path.name}: {e}")
        print(f"🔍 Error details: {type(e).__name__}")

    print("-" * 50)

print("\n" + "="*60)
print("📋 PROCESSING SUMMARY")
print("="*60)
print(f"✅ Successfully processed {len(pdf_files)} PDF files")
if GPU_AVAILABLE:
    print(f"🎮 Processing mode: GPU-accelerated ({DEVICE_NAME})")
else:
    print("🖥️  Processing mode: CPU-only (no GPU acceleration)")
print("🔍 Features used: OCR, Table Structure Recognition, Image Interpretation")
print("📝 Output format: Markdown")
print("\n✅ All specified PDFs have been processed successfully!")