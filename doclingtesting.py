import os
import warnings
import time
from pathlib import Path
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.document_converter import PdfFormatOption

# Try to import torch to check for GPU availability
try:
    import torch
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE_NAME = torch.cuda.get_device_name(0) if GPU_AVAILABLE else "CPU"
    DEVICE_COUNT = torch.cuda.device_count() if GPU_AVAILABLE else 0
except ImportError:
    GPU_AVAILABLE = False
    DEVICE_NAME = "CPU"
    DEVICE_COUNT = 0

# Only suppress pin_memory warnings when using CPU (no GPU available)
if not GPU_AVAILABLE:
    warnings.filterwarnings("ignore", message=".*pin_memory.*")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")

# --- Configuration ---

# 1. Determine the path to your Desktop
# This works for Windows, macOS, and Linux
desktop_path = Path.home() / "Desktop"
# Filter to only look for PDF files
pdf_files = list(desktop_path.glob("*.pdf"))

if not pdf_files:
    print(f"ERROR: No PDF files found in {desktop_path}")
    exit()

# 2. Initialize the Document Converter with optimal settings
# Configure pipeline options for processing
pipeline_options = PdfPipelineOptions()
pipeline_options.do_ocr = True  # Enable OCR for image text extraction
pipeline_options.do_table_structure = True  # Enable table structure recognition

# Create format options for PDF processing
pdf_format_option = PdfFormatOption(
    pipeline_options=pipeline_options
)

# Initialize converter with optimal configuration (will use GPU if available)
converter = DocumentConverter(
    format_options={
        InputFormat.PDF: pdf_format_option
    }
)

# Display device information
print("🖥️  DEVICE INFORMATION")
print("="*50)
if GPU_AVAILABLE:
    print(f"✅ GPU acceleration: ENABLED")
    print(f"🎮 GPU device: {DEVICE_NAME}")
    print(f"🔢 GPU count: {DEVICE_COUNT}")
    print(f"💾 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
else:
    print(f"⚠️  GPU acceleration: NOT AVAILABLE")
    print(f"🖥️  Fallback device: CPU")
    print(f"ℹ️  Note: Install CUDA-compatible PyTorch for GPU acceleration")
print("="*50)

# --- Processing ---

print("-" * 50)
print(f"Found {len(pdf_files)} PDF(s) on the Desktop. Starting conversion...")
print("-" * 50)

# Initialize timing statistics
processing_times = []
document_stats = []  # Store document statistics for final summary
start_total_time = time.time()

# Iterate over each PDF file found
for i, pdf_path in enumerate(pdf_files, 1):
    print(f"\n📄 Processing ({i}/{len(pdf_files)}): {pdf_path.name}")
    print(f"📁 File size: {pdf_path.stat().st_size / 1024:.1f} KB")
    print("-" * 20)

    try:
        print("🔄 Converting document...")
        # Start timing for this document
        start_time = time.time()

        # Convert the document using its local file path
        result = converter.convert(str(pdf_path))

        # Access the structured document object
        doc = result.document

        print("📝 Extracting content...")
        # Export the document content as Markdown for clean, readable text
        markdown_content = doc.export_to_markdown()

        # Calculate processing time
        end_time = time.time()
        processing_time = end_time - start_time
        processing_times.append(processing_time)

        # Store document statistics
        num_pages = len(doc.pages) if hasattr(doc, 'pages') else 1
        document_stats.append({
            'name': pdf_path.name,
            'pages': num_pages,
            'content_length': len(markdown_content),
            'processing_time': processing_time,
            'file_size_kb': pdf_path.stat().st_size / 1024
        })

        # Print document statistics
        print(f"✅ Extraction complete!")
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Content length: {len(markdown_content)} characters")
        print(f"📄 Number of pages: {num_pages}")
        print("\n" + "="*60)
        print("EXTRACTED CONTENT:")
        print("="*60)

        # Print the extracted content
        print(markdown_content)

    except Exception as e:
        print(f"❌ An error occurred processing {pdf_path.name}: {e}")
        print(f"🔍 Error details: {type(e).__name__}")
        # Add a placeholder time for failed processing
        processing_times.append(0.0)

    print("-" * 50)

# Calculate total processing time and statistics
end_total_time = time.time()
total_time = end_total_time - start_total_time

# Filter out failed processing times (0.0) for statistics
successful_times = [t for t in processing_times if t > 0]

print("\n" + "="*60)
print("📋 PROCESSING SUMMARY")
print("="*60)
print(f"✅ Successfully processed {len(pdf_files)} PDF files")
if GPU_AVAILABLE:
    print(f"🎮 Processing mode: GPU-accelerated ({DEVICE_NAME})")
else:
    print("🖥️  Processing mode: CPU-only (no GPU acceleration)")
print("🔍 Features used: OCR, Table Structure Recognition, Image Interpretation")
print("📝 Output format: Markdown")

print("\n" + "⏱️ " + " TIMING STATISTICS " + "⏱️ ")
print("="*60)
print(f"📊 Total processing time: {total_time:.2f} seconds")
if successful_times:
    print(f"📈 Average time per document: {sum(successful_times)/len(successful_times):.2f} seconds")
    print(f"🚀 Fastest document: {min(successful_times):.2f} seconds")
    print(f"🐌 Slowest document: {max(successful_times):.2f} seconds")
    print(f"📋 Individual times: {[f'{t:.2f}s' for t in processing_times]}")

    # Calculate processing speed and page statistics
    total_pages = sum(stat['pages'] for stat in document_stats)
    total_content_chars = sum(stat['content_length'] for stat in document_stats)
    total_file_size_kb = sum(stat['file_size_kb'] for stat in document_stats)

    print(f"📄 Processing speed: {len(successful_times)/sum(successful_times):.2f} documents/second")
    print(f"📑 Page processing rate: {total_pages/sum(successful_times):.2f} pages/second")
    print(f"📝 Character extraction rate: {total_content_chars/sum(successful_times):.0f} chars/second")
    print(f"💾 Data throughput: {total_file_size_kb/sum(successful_times):.1f} KB/second")

    # Find fastest and slowest documents by name
    fastest_doc = min(document_stats, key=lambda x: x['processing_time'])
    slowest_doc = max(document_stats, key=lambda x: x['processing_time'])
    print(f"🏆 Fastest: {fastest_doc['name']} ({fastest_doc['processing_time']:.2f}s)")
    print(f"🐢 Slowest: {slowest_doc['name']} ({slowest_doc['processing_time']:.2f}s)")

print("\n✅ All specified PDFs have been processed successfully!")