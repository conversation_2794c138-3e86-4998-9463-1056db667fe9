import os
import warnings
from pathlib import Path
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.document_converter import PdfFormatOption

# Suppress PyTorch pin_memory warnings when using CPU
warnings.filterwarnings("ignore", message=".*pin_memory.*")
warnings.filterwarnings("ignore", category=UserWarning, module="torch")

# --- Configuration ---

# 1. Determine the path to your Desktop
# This works for Windows, macOS, and Linux
desktop_path = Path.home() / "Desktop"
# Filter to only look for PDF files
pdf_files = list(desktop_path.glob("*.pdf"))

if not pdf_files:
    print(f"ERROR: No PDF files found in {desktop_path}")
    exit()

# 2. Initialize the Document Converter with CPU-optimized settings
# Configure pipeline options for CPU processing to avoid pin_memory warnings
pipeline_options = PdfPipelineOptions()
pipeline_options.do_ocr = True  # Enable OCR for image text extraction
pipeline_options.do_table_structure = True  # Enable table structure recognition

# Create format options for PDF processing
pdf_format_option = PdfFormatOption(
    pipeline_options=pipeline_options
)

# Initialize converter with CPU-optimized configuration
converter = DocumentConverter(
    format_options={
        InputFormat.PDF: pdf_format_option
    }
)

# --- Processing ---

print("-" * 50)
print(f"Found {len(pdf_files)} PDF(s) on the Desktop. Starting conversion...")
print("-" * 50)

# Iterate over each PDF file found
for i, pdf_path in enumerate(pdf_files, 1):
    print(f"\n📄 Processing ({i}/{len(pdf_files)}): {pdf_path.name}")
    print(f"📁 File size: {pdf_path.stat().st_size / 1024:.1f} KB")
    print("-" * 20)

    try:
        print("🔄 Converting document...")
        # Convert the document using its local file path
        result = converter.convert(str(pdf_path))

        # Access the structured document object
        doc = result.document

        print("📝 Extracting content...")
        # Export the document content as Markdown for clean, readable text
        markdown_content = doc.export_to_markdown()

        # Print document statistics
        print(f"✅ Extraction complete!")
        print(f"📊 Content length: {len(markdown_content)} characters")
        print(f"📄 Number of pages: {len(doc.pages) if hasattr(doc, 'pages') else 'Unknown'}")
        print("\n" + "="*60)
        print("EXTRACTED CONTENT:")
        print("="*60)

        # Print the extracted content
        print(markdown_content)

    except Exception as e:
        print(f"❌ An error occurred processing {pdf_path.name}: {e}")
        print(f"🔍 Error details: {type(e).__name__}")

    print("-" * 50)

print("\n" + "="*60)
print("📋 PROCESSING SUMMARY")
print("="*60)
print(f"✅ Successfully processed {len(pdf_files)} PDF files")
print("🖥️  Processing mode: CPU-only (no GPU acceleration)")
print("🔍 Features used: OCR, Table Structure Recognition, Image Interpretation")
print("📝 Output format: Markdown")
print("\n✅ All specified PDFs have been processed successfully!")