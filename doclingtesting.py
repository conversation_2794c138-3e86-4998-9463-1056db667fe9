import os
from pathlib import Path
from docling.document_converter import DocumentConverter

# --- Configuration ---

# 1. Determine the path to your Desktop
# This works for Windows, macOS, and Linux
desktop_path = Path.home() / "Desktop"
# Filter to only look for PDF files
pdf_files = list(desktop_path.glob("*.pdf"))

if not pdf_files:
    print(f"ERROR: No PDF files found in {desktop_path}")
    exit()

# 2. Initialize the Document Converter
# Docling may download models on the first run.
converter = DocumentConverter()

# --- Processing ---

print("-" * 50)
print(f"Found {len(pdf_files)} PDF(s) on the Desktop. Starting conversion...")
print("-" * 50)

# Iterate over each PDF file found
for pdf_path in pdf_files:
    print(f"\n📄 Processing: {pdf_path.name}")
    print("-" * 20)

    try:
        # Convert the document using its local file path
        result = converter.convert(str(pdf_path))

        # Access the structured document object
        doc = result.document

        # Export the document content as Markdown for clean, readable text
        markdown_content = doc.export_to_markdown()

        # Print the extracted content
        print(markdown_content)

    except Exception as e:
        print(f"❌ An error occurred processing {pdf_path.name}: {e}")

    print("-" * 50)

print("✅ All specified PDFs have been processed.")