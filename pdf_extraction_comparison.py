import os
import time
import warnings
from pathlib import Path

# Docling imports
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, EasyOcrOptions
from docling.datamodel.accelerator_options import AcceleratorDevice, AcceleratorOptions

# Try to import torch for GPU detection
try:
    import torch
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE_NAME = torch.cuda.get_device_name(0) if GPU_AVAILABLE else "CPU"
    DEVICE_COUNT = torch.cuda.device_count() if GPU_AVAILABLE else 0
    if GPU_AVAILABLE:
        GPU_MEMORY = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
except ImportError:
    GPU_AVAILABLE = False
    DEVICE_NAME = "CPU"
    DEVICE_COUNT = 0
    GPU_MEMORY = 0

# Only suppress pin_memory warnings when using CPU
if not GPU_AVAILABLE:
    warnings.filterwarnings("ignore", message=".*pin_memory.*")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")

def install_comparison_libraries():
    """Install comparison libraries if not available"""
    import subprocess
    import sys
    
    libraries = [
        "PyPDF2",
        "pdfplumber", 
        "pymupdf",
        "pdfminer.six"
    ]
    
    for lib in libraries:
        try:
            if lib == "pymupdf":
                import fitz
            elif lib == "pdfminer.six":
                import pdfminer
            else:
                __import__(lib)
            print(f"✅ {lib} already installed")
        except ImportError:
            print(f"📦 Installing {lib}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", lib])
                print(f"✅ {lib} installed successfully")
            except Exception as e:
                print(f"❌ Failed to install {lib}: {e}")

def extract_with_pypdf2(pdf_path):
    """Extract text using PyPDF2"""
    try:
        import PyPDF2
        start_time = time.time()
        
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
        
        processing_time = time.time() - start_time
        return text.strip(), processing_time, len(reader.pages)
    except Exception as e:
        return f"Error: {e}", 0, 0

def extract_with_pdfplumber(pdf_path):
    """Extract text using pdfplumber"""
    try:
        import pdfplumber
        start_time = time.time()
        
        text = ""
        page_count = 0
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
                page_count += 1
        
        processing_time = time.time() - start_time
        return text.strip(), processing_time, page_count
    except Exception as e:
        return f"Error: {e}", 0, 0

def extract_with_pymupdf(pdf_path):
    """Extract text using PyMuPDF (fitz)"""
    try:
        import fitz  # PyMuPDF
        start_time = time.time()
        
        doc = fitz.open(pdf_path)
        text = ""
        for page in doc:
            text += page.get_text() + "\n"
        
        processing_time = time.time() - start_time
        page_count = len(doc)
        doc.close()
        return text.strip(), processing_time, page_count
    except Exception as e:
        return f"Error: {e}", 0, 0

def extract_with_pdfminer(pdf_path):
    """Extract text using pdfminer.six"""
    try:
        from pdfminer.high_level import extract_text
        start_time = time.time()
        
        text = extract_text(str(pdf_path))
        processing_time = time.time() - start_time
        
        # Count pages (approximate)
        page_count = text.count('\f') + 1 if text else 0
        
        return text.strip(), processing_time, page_count
    except Exception as e:
        return f"Error: {e}", 0, 0

def extract_with_docling_basic(pdf_path):
    """Extract text using Docling with basic settings"""
    try:
        start_time = time.time()
        
        # Basic Docling configuration
        converter = DocumentConverter()
        result = converter.convert(str(pdf_path))
        doc = result.document
        text = doc.export_to_markdown()
        
        processing_time = time.time() - start_time
        page_count = len(doc.pages) if hasattr(doc, 'pages') else 0
        
        return text, processing_time, page_count
    except Exception as e:
        return f"Error: {e}", 0, 0

def extract_with_docling_enhanced(pdf_path):
    """Extract text using Docling with enhanced OCR"""
    try:
        start_time = time.time()
        
        # Configure enhanced pipeline options
        pipeline_options = PdfPipelineOptions()
        pipeline_options.do_ocr = True
        pipeline_options.do_table_structure = True
        pipeline_options.table_structure_options.do_cell_matching = True
        
        # Configure EasyOCR with force full page OCR for maximum text extraction
        ocr_options = EasyOcrOptions(
            force_full_page_ocr=True,
            lang=["en"]
        )
        pipeline_options.ocr_options = ocr_options
        
        # Configure accelerator options
        pipeline_options.accelerator_options = AcceleratorOptions(
            num_threads=4,
            device=AcceleratorDevice.AUTO
        )
        
        # Create converter
        converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
            }
        )
        
        # Convert document
        result = converter.convert(str(pdf_path))
        doc = result.document
        text = doc.export_to_markdown()
        
        processing_time = time.time() - start_time
        page_count = len(doc.pages) if hasattr(doc, 'pages') else 0
        
        return text, processing_time, page_count
    except Exception as e:
        return f"Error: {e}", 0, 0

def compare_extractions(pdf_path):
    """Compare text extraction from different libraries"""
    print(f"\n{'='*80}")
    print(f"📄 COMPARING EXTRACTION FOR: {pdf_path.name}")
    print(f"📁 File size: {pdf_path.stat().st_size / 1024:.1f} KB")
    print(f"{'='*80}")
    
    extractors = [
        ("PyPDF2", extract_with_pypdf2),
        ("pdfplumber", extract_with_pdfplumber), 
        ("PyMuPDF (fitz)", extract_with_pymupdf),
        ("pdfminer.six", extract_with_pdfminer),
        ("Docling (Basic)", extract_with_docling_basic),
        ("Docling (Enhanced OCR)", extract_with_docling_enhanced)
    ]
    
    results = {}
    
    for name, extractor in extractors:
        print(f"\n🔄 Extracting with {name}...")
        text, proc_time, pages = extractor(pdf_path)
        
        results[name] = {
            'text': text,
            'time': proc_time,
            'pages': pages,
            'char_count': len(text) if isinstance(text, str) else 0,
            'word_count': len(text.split()) if isinstance(text, str) and not text.startswith("Error:") else 0
        }
        
        print(f"   ⏱️  Time: {proc_time:.2f}s")
        print(f"   📄 Pages: {pages}")
        print(f"   📝 Characters: {results[name]['char_count']:,}")
        print(f"   📖 Words: {results[name]['word_count']:,}")
        
        if text.startswith("Error:"):
            print(f"   ❌ {text}")
    
    return results

def print_text_samples(results, sample_length=500):
    """Print sample text from each extractor for comparison"""
    print(f"\n{'='*80}")
    print("📝 TEXT SAMPLE COMPARISON (First 500 characters)")
    print(f"{'='*80}")
    
    for name, result in results.items():
        text = result['text']
        if not text.startswith("Error:") and text:
            sample = text[:sample_length] + "..." if len(text) > sample_length else text
            print(f"\n🔹 {name}:")
            print("-" * 40)
            print(sample)
        else:
            print(f"\n🔹 {name}: {text}")

def main():
    print("🔧 Installing comparison libraries...")
    install_comparison_libraries()
    
    print(f"\n🖥️  DEVICE INFORMATION")
    print("="*50)
    if GPU_AVAILABLE:
        print(f"✅ GPU acceleration: ENABLED")
        print(f"🎮 GPU device: {DEVICE_NAME}")
        print(f"🔢 GPU count: {DEVICE_COUNT}")
        print(f"💾 GPU memory: {GPU_MEMORY:.1f} GB")
    else:
        print(f"⚠️  GPU acceleration: NOT AVAILABLE")
        print(f"🖥️  Fallback device: CPU")
    print("="*50)
    
    # Find PDF files on desktop
    desktop_path = Path.home() / "Desktop"
    pdf_files = list(desktop_path.glob("*.pdf"))
    
    if not pdf_files:
        print(f"ERROR: No PDF files found in {desktop_path}")
        return
    
    print(f"\n📋 Found {len(pdf_files)} PDF file(s) for comparison")
    
    # Process first PDF for detailed comparison
    pdf_path = pdf_files[0]
    print(f"\n🎯 Detailed comparison using: {pdf_path.name}")
    
    results = compare_extractions(pdf_path)
    print_text_samples(results)
    
    print(f"\n✅ Comparison complete!")
    print(f"📊 Docling Enhanced OCR should show superior text extraction with:")
    print(f"   • Better handling of images and scanned content")
    print(f"   • Improved table structure recognition")
    print(f"   • More comprehensive text extraction from complex layouts")

if __name__ == "__main__":
    main()
